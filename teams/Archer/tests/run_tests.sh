#!/bin/bash

# commit-msg hook 测试脚本
# 用法: ./run_tests.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEAM_DIR="$(dirname "$SCRIPT_DIR")"
COMMIT_MSG_HOOK="$TEAM_DIR/commit-msg"

echo "========================================"
echo "commit-msg Hook 测试套件"
echo "========================================"
echo "测试目录: $SCRIPT_DIR"
echo "Hook脚本: $COMMIT_MSG_HOOK"
echo ""

# 检查commit-msg脚本是否存在
if [ ! -f "$COMMIT_MSG_HOOK" ]; then
    echo -e "${RED}错误: commit-msg hook 不存在: $COMMIT_MSG_HOOK${NC}"
    exit 1
fi

# 确保commit-msg脚本可执行
chmod +x "$COMMIT_MSG_HOOK"

# 创建临时目录用于测试
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# 初始化临时git仓库
cd "$TEMP_DIR"
git init --quiet
git config user.name "Test User"
git config user.email "<EMAIL>"

# 创建一个初始提交
echo "test" > test.txt
git add test.txt
git commit -m "Initial commit" --quiet

echo "开始运行测试..."
echo ""

# 运行测试函数
run_test() {
    local test_file="$1"
    local test_name=$(basename "$test_file" .txt)
    local expected_result=""
    
    # 根据文件名判断期望结果
    if [[ "$test_file" == *.accept.txt ]]; then
        expected_result="accept"
    elif [[ "$test_file" == *.deny.txt ]]; then
        expected_result="deny"
    else
        echo -e "${YELLOW}跳过: $test_name (未知测试类型)${NC}"
        return
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 复制测试消息到临时文件
    local temp_msg="$TEMP_DIR/commit_msg_test"
    cp "$test_file" "$temp_msg"
    
    # 运行commit-msg hook
    local exit_code=0
    "$COMMIT_MSG_HOOK" "$temp_msg" 2>/dev/null || exit_code=$?
    
    # 检查结果
    if [ "$expected_result" = "accept" ]; then
        if [ $exit_code -eq 0 ]; then
            echo -e "${GREEN}✓ PASS${NC}: $test_name"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ FAIL${NC}: $test_name (期望通过但被拒绝)"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else # deny
        if [ $exit_code -ne 0 ]; then
            echo -e "${GREEN}✓ PASS${NC}: $test_name"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}✗ FAIL${NC}: $test_name (期望拒绝但通过了)"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    fi
}

# 运行所有测试文件
for test_file in "$SCRIPT_DIR"/*.txt; do
    if [ -f "$test_file" ]; then
        run_test "$test_file"
    fi
done

echo ""
echo "========================================"
echo "测试结果汇总"
echo "========================================"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}所有测试通过! ✓${NC}"
    exit 0
else
    echo -e "${RED}有 $FAILED_TESTS 个测试失败! ✗${NC}"
    exit 1
fi
